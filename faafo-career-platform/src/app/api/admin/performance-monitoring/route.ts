/**
 * Enhanced Performance Monitoring API
 * 
 * Provides comprehensive performance metrics, alerts, and system health data
 * for the admin performance monitoring dashboard.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { requireAdmin } from '@/lib/auth-utils';
import { performanceMonitor } from '@/lib/performance-monitor';
import { advancedCacheManager } from '@/lib/advanced-cache-manager';
import { requestOptimizer } from '@/lib/request-optimizer';
import { aiServiceMonitor } from '@/lib/ai-service-monitor';
import { enhancedAlertingService } from '@/lib/enhanced-alerting-service';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

interface PerformanceMetrics {
  timestamp: string;
  overview: {
    systemHealth: string;
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    cacheHitRate: number;
    activeRequests: number;
    queuedRequests: number;
    performanceScore: number;
  };
  quickStats: {
    uptime: number;
    errorRate: number;
    throughput: number;
    memoryUsage: number;
  };
  detailed: {
    performance: any;
    cache: any;
    requests: any;
    aiService: any;
  };
}

/**
 * GET /api/admin/performance-monitoring
 * 
 * Retrieves performance monitoring data based on view and time range
 */
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Require admin authentication
  await requireAdmin();

  const { searchParams } = new URL(request.url);
  const view = searchParams.get('view') || 'overview';
  const timeRange = searchParams.get('timeRange') || '1h';

  try {
    switch (view) {
      case 'overview':
        return NextResponse.json({
          success: true,
          data: await getOverviewData(timeRange)
        });

      case 'alerts':
        return NextResponse.json({
          success: true,
          data: await getAlertsData()
        });

      case 'performance':
        return NextResponse.json({
          success: true,
          data: await getPerformanceData(timeRange)
        });

      case 'system':
        return NextResponse.json({
          success: true,
          data: await getSystemData()
        });

      case 'metrics':
        return NextResponse.json({
          success: true,
          data: await getMetricsData(timeRange)
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid view parameter'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Performance monitoring API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch performance data'
    }, { status: 500 });
  }
});

/**
 * POST /api/admin/performance-monitoring
 *
 * Handles performance monitoring actions (acknowledge alerts, etc.)
 */
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Require admin authentication
  const { userId } = await requireAdmin();

  // Get session to access user email
  const session = await getServerSession(authOptions);
  const userEmail = session?.user?.email || 'admin';

  try {
    const body = await request.json();
    const { action, ...params } = body;

    switch (action) {
      case 'acknowledge_alert':
        const acknowledged = enhancedAlertingService.acknowledgeAlert(
          params.alertId,
          userEmail
        );

        return NextResponse.json({
          success: acknowledged,
          message: acknowledged ? 'Alert acknowledged' : 'Failed to acknowledge alert'
        });

      case 'resolve_alert':
        const resolved = enhancedAlertingService.resolveAlert(
          params.alertId,
          userEmail
        );
        
        return NextResponse.json({
          success: resolved,
          message: resolved ? 'Alert resolved' : 'Failed to resolve alert'
        });

      case 'test_notification':
        await enhancedAlertingService.createAlert({
          title: 'Test Alert',
          message: 'This is a test alert from the performance monitoring dashboard',
          severity: 'low',
          type: 'PERFORMANCE',
          source: 'dashboard_test',
          metadata: { test: true }
        });
        
        return NextResponse.json({
          success: true,
          message: 'Test alert created'
        });

      case 'clear_cache':
        await advancedCacheManager.clearAll();
        
        return NextResponse.json({
          success: true,
          message: 'Cache cleared successfully'
        });

      case 'optimize_requests':
        await requestOptimizer.optimizeQueue();
        
        return NextResponse.json({
          success: true,
          message: 'Request optimization triggered'
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Performance monitoring action error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to execute action'
    }, { status: 500 });
  }
});

/**
 * Get overview performance data
 */
async function getOverviewData(timeRange: string): Promise<PerformanceMetrics> {
  const [
    performanceInsights,
    cacheStats,
    requestStats,
    aiServiceStats
  ] = await Promise.all([
    performanceMonitor.getInsights(),
    advancedCacheManager.getStats(),
    requestOptimizer.getStats(),
    aiServiceMonitor.getMetrics()
  ]);

  // Calculate system health
  const systemHealth = calculateSystemHealth({
    performance: performanceInsights,
    cache: cacheStats,
    requests: requestStats,
    aiService: aiServiceStats
  });

  // Calculate performance score
  const performanceScore = calculatePerformanceScore({
    performance: performanceInsights,
    cache: cacheStats,
    requests: requestStats,
    aiService: aiServiceStats
  });

  return {
    timestamp: new Date().toISOString(),
    overview: {
      systemHealth,
      totalRequests: aiServiceStats.totalRequests || 0,
      successRate: aiServiceStats.totalRequests > 0
        ? ((aiServiceStats.totalRequests - aiServiceStats.failedRequests) / aiServiceStats.totalRequests) * 100
        : 100,
      averageResponseTime: aiServiceStats.averageResponseTime || requestStats.averageWaitTime || 0,
      cacheHitRate: cacheStats.hitRate || 0,
      activeRequests: requestOptimizer.getActiveRequestCount(),
      queuedRequests: requestOptimizer.getQueuedRequestCount(),
      performanceScore
    },
    quickStats: {
      uptime: process.uptime() * 1000, // Convert to milliseconds
      errorRate: aiServiceStats.totalRequests > 0 
        ? (aiServiceStats.failedRequests / aiServiceStats.totalRequests) * 100 
        : 0,
      throughput: requestStats.throughputPerSecond || 0,
      memoryUsage: cacheStats.memoryUsage || 0
    },
    detailed: {
      performance: performanceInsights,
      cache: cacheStats,
      requests: requestStats,
      aiService: aiServiceStats
    }
  };
}

/**
 * Get alerts data
 */
async function getAlertsData() {
  const alerts = enhancedAlertingService.getActiveAlerts();
  const stats = enhancedAlertingService.getAlertStats();

  return {
    alerts,
    stats,
    timestamp: new Date().toISOString()
  };
}

/**
 * Get detailed performance data
 */
async function getPerformanceData(timeRange: string) {
  const performanceInsights = await performanceMonitor.getInsights();
  
  // Generate time series data based on time range
  const timeSeriesData = generateTimeSeriesData(timeRange, performanceInsights);

  return {
    insights: performanceInsights,
    timeSeries: timeSeriesData,
    timestamp: new Date().toISOString()
  };
}

/**
 * Get system data
 */
async function getSystemData() {
  const [
    cacheStats,
    requestStats,
    aiServiceStats
  ] = await Promise.all([
    advancedCacheManager.getStats(),
    requestOptimizer.getStats(),
    aiServiceMonitor.getMetrics()
  ]);

  return {
    cache: cacheStats,
    requests: requestStats,
    aiService: aiServiceStats,
    system: {
      uptime: process.uptime() * 1000,
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    },
    timestamp: new Date().toISOString()
  };
}

/**
 * Get comprehensive metrics data
 */
async function getMetricsData(timeRange: string) {
  const overviewData = await getOverviewData(timeRange);
  const alertsData = await getAlertsData();
  const systemData = await getSystemData();

  return {
    overview: overviewData,
    alerts: alertsData,
    system: systemData,
    timestamp: new Date().toISOString()
  };
}

/**
 * Calculate overall system health
 */
function calculateSystemHealth(stats: any): string {
  const {
    performance,
    cache,
    requests,
    aiService
  } = stats;

  let healthScore = 100;

  // Check response time (deduct points for slow responses)
  if (performance.averageResponseTime > 2000) {
    healthScore -= 30; // Critical
  } else if (performance.averageResponseTime > 1000) {
    healthScore -= 15; // Warning
  }

  // Check error rate
  const errorRate = aiService.totalRequests > 0 
    ? (aiService.failedRequests / aiService.totalRequests) * 100 
    : 0;
  
  if (errorRate > 5) {
    healthScore -= 40; // Critical
  } else if (errorRate > 1) {
    healthScore -= 20; // Warning
  }

  // Check cache hit rate
  if (cache.hitRate < 0.5) {
    healthScore -= 20; // Poor cache performance
  } else if (cache.hitRate < 0.7) {
    healthScore -= 10; // Suboptimal cache performance
  }

  // Check queue length
  const queueLength = requestOptimizer.getQueuedRequestCount();
  if (queueLength > 100) {
    healthScore -= 25; // High queue
  } else if (queueLength > 50) {
    healthScore -= 10; // Moderate queue
  }

  // Determine health status
  if (healthScore >= 90) return 'Healthy';
  if (healthScore >= 70) return 'Warning';
  return 'Critical';
}

/**
 * Calculate performance score
 */
function calculatePerformanceScore(stats: any): number {
  const {
    performance,
    cache,
    requests,
    aiService
  } = stats;

  let score = 100;

  // Response time score (40% weight)
  const responseTimeScore = Math.max(0, 100 - (performance.averageResponseTime / 20));
  score = score * 0.4 + responseTimeScore * 0.4;

  // Error rate score (30% weight)
  const errorRate = aiService.totalRequests > 0 
    ? (aiService.failedRequests / aiService.totalRequests) * 100 
    : 0;
  const errorScore = Math.max(0, 100 - (errorRate * 10));
  score = score * 0.7 + errorScore * 0.3;

  // Cache hit rate score (20% weight)
  const cacheScore = (cache.hitRate || 0) * 100;
  score = score * 0.8 + cacheScore * 0.2;

  // Throughput score (10% weight)
  const throughputScore = Math.min(100, (requests.throughputPerSecond || 0) * 2);
  score = score * 0.9 + throughputScore * 0.1;

  return Math.round(Math.max(0, Math.min(100, score)));
}

/**
 * Generate time series data for charts
 */
function generateTimeSeriesData(timeRange: string, performanceInsights: any) {
  // This is a simplified implementation
  // In a real system, you'd fetch historical data from a time-series database
  
  const now = Date.now();
  const intervals = getTimeIntervals(timeRange);
  
  return {
    responseTime: intervals.map((time, index) => ({
      timestamp: time,
      value: performanceInsights.averageResponseTime + (Math.random() - 0.5) * 200
    })),
    throughput: intervals.map((time, index) => ({
      timestamp: time,
      value: Math.max(0, 10 + (Math.random() - 0.5) * 5)
    })),
    errorRate: intervals.map((time, index) => ({
      timestamp: time,
      value: Math.max(0, Math.random() * 2)
    })),
    cacheHitRate: intervals.map((time, index) => ({
      timestamp: time,
      value: Math.max(0.5, Math.min(1, 0.8 + (Math.random() - 0.5) * 0.3))
    }))
  };
}

/**
 * Get time intervals for the specified range
 */
function getTimeIntervals(timeRange: string): number[] {
  const now = Date.now();
  const intervals: number[] = [];
  
  let duration: number;
  let intervalSize: number;
  
  switch (timeRange) {
    case '1h':
      duration = 60 * 60 * 1000; // 1 hour
      intervalSize = 5 * 60 * 1000; // 5 minutes
      break;
    case '6h':
      duration = 6 * 60 * 60 * 1000; // 6 hours
      intervalSize = 30 * 60 * 1000; // 30 minutes
      break;
    case '24h':
      duration = 24 * 60 * 60 * 1000; // 24 hours
      intervalSize = 60 * 60 * 1000; // 1 hour
      break;
    case '7d':
      duration = 7 * 24 * 60 * 60 * 1000; // 7 days
      intervalSize = 6 * 60 * 60 * 1000; // 6 hours
      break;
    default:
      duration = 60 * 60 * 1000; // Default to 1 hour
      intervalSize = 5 * 60 * 1000; // 5 minutes
  }
  
  const startTime = now - duration;
  
  for (let time = startTime; time <= now; time += intervalSize) {
    intervals.push(time);
  }
  
  return intervals;
}
