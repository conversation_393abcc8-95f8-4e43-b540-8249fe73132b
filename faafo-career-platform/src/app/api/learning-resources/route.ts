import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { createSuccessResponse, ErrorResponses, createPaginationMeta } from '@/lib/api-response';
import { validateInput, paginationSchema, resourceFilterSchema } from '@/lib/validation';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { withCSRFProtection } from '@/lib/csrf';
import { apiCache } from '@/lib/cache';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { advancedPaginationService } from '@/lib/services/advanced-pagination-service';
import { extractPaginationParams, generatePaginationCacheKey, createPaginatedApiResponse } from '@/lib/pagination-utils';

interface LearningResourcesResponse {
  data: any[];
  meta: any;
}

interface LearningResourceCreateResponse {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  skillLevel: string;
  author?: string | null;
  duration?: string | null;
  cost: string;
  format: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.search)(request);
  if (!rateLimitResult.allowed) {
    const error = new Error('Too many requests') as any;
    error.statusCode = 429;
    throw error;
  }

  // Extract pagination parameters using advanced pagination utils
  const paginationOptions = extractPaginationParams(request, 'hybrid');

  // Extract filter parameters
  const { searchParams } = new URL(request.url);
  const filters = {
    category: searchParams.get('category'),
    type: searchParams.get('type'),
    skillLevel: searchParams.get('skillLevel'),
    cost: searchParams.get('cost'),
    format: searchParams.get('format'),
    search: searchParams.get('search')
  };

  // Generate cache key using advanced pagination cache key generator
  const cacheKey = generatePaginationCacheKey('learning_resources', paginationOptions, filters);

  // Validate pagination parameters
  const paginationValidation = paginationSchema.safeParse({
    page: searchParams.get('page'),
    limit: searchParams.get('limit'),
  });

  if (!paginationValidation.success) {
    const error = new Error(paginationValidation.error.message) as any;
    error.statusCode = 400;
    throw error;
  }

  const { page = 1, limit = 10 } = paginationValidation.data;

  // Validate filter parameters
  const filterValidation = resourceFilterSchema.safeParse({
    category: searchParams.get('category'),
    skillLevel: searchParams.get('skillLevel'),
    type: searchParams.get('type'),
    cost: searchParams.get('cost'),
    search: searchParams.get('search'),
  });

  if (!filterValidation.success) {
    const error = new Error(filterValidation.error.message) as any;
    error.statusCode = 400;
    throw error;
  }

  const { category, skillLevel, type, cost, search } = filterValidation.data;

  const where: Record<string, unknown> = {
    isActive: true,
  };

  if (category && category !== 'all') {
    where.category = category.toUpperCase();
  }

  if (skillLevel && skillLevel !== 'all') {
    where.skillLevel = skillLevel.toUpperCase();
  }

  if (type && type !== 'all') {
    where.type = type.toUpperCase();
  }

  if (cost && cost !== 'all') {
    where.cost = cost.toUpperCase();
  }

  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
      { author: { contains: search, mode: 'insensitive' } },
    ];
  }

  // Use advanced pagination service for optimal performance
  const result = await advancedPaginationService.smartPaginate(
    'learningResource',
    {
      offset: async (skip: number, take: number) => {
        return await prisma.learningResource.findMany({
          where,
          skip,
          take,
          orderBy: [
            { category: 'asc' },
            { skillLevel: 'asc' },
            { title: 'asc' }
          ],
          select: {
            id: true,
            title: true,
            description: true,
            url: true,
            type: true,
            category: true,
            skillLevel: true,
            author: true,
            duration: true,
            cost: true,
            format: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
            careerPaths: {
              select: {
                id: true,
                name: true,
                slug: true
              }
            },
            skills: {
              select: {
                id: true,
                name: true
              }
            }
          }
        });
      },
      cursor: async (cursor?: any, take?: number) => {
        return await prisma.learningResource.findMany({
          where: cursor ? { AND: [where, { id: { gt: cursor } }] } : where,
          take: take || 10,
          orderBy: { id: 'asc' },
          select: {
            id: true,
            title: true,
            description: true,
            url: true,
            type: true,
            category: true,
            skillLevel: true,
            author: true,
            duration: true,
            cost: true,
            format: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
            careerPaths: {
              select: {
                id: true,
                name: true,
                slug: true
              }
            },
            skills: {
              select: {
                id: true,
                name: true
              }
            }
          }
        });
      },
      count: async () => await prisma.learningResource.count({ where })
    },
    where,
    paginationOptions,
    { cursorField: 'id', orderDirection: 'asc' },
    cacheKey
  );

  // Add default rating values (optimize this later if needed)
  const resourcesWithRatings = result.data.map(resource => ({
    ...resource,
    averageRating: 0,
    totalRatings: 0
  }));

  // Update result data with ratings
  const finalResult = {
    ...result,
    data: resourcesWithRatings
  };

  return createPaginatedApiResponse(finalResult, 'Learning resources retrieved successfully');
});

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<LearningResourceCreateResponse>>> => {
  return withCSRFProtection(request, async (): Promise<NextResponse<ApiResponse<LearningResourceCreateResponse>>> => {
    const body = await request.json();

    const {
      title,
      description,
      url,
      type,
      category,
      skillLevel,
      author,
      duration,
      cost = 'FREE',
      format
    } = body;

    // Validate required fields
    if (!title || !description || !url || !type || !category || !skillLevel || !format) {
      const error = new Error('Missing required fields') as any;
      error.statusCode = 400;
      throw error;
    }

    const resource = await prisma.learningResource.create({
      data: {
        title,
        description,
        url,
        type: type.toUpperCase(),
        category: category.toUpperCase(),
        skillLevel: skillLevel.toUpperCase(),
        author,
        duration,
        cost: cost.toUpperCase(),
        format: format.toUpperCase()
      }
    });

    return NextResponse.json({
      success: true,
      data: resource
    }, { status: 201 });
  }) as Promise<NextResponse<ApiResponse<LearningResourceCreateResponse>>>;
});
