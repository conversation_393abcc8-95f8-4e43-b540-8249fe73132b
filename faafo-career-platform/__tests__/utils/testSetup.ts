import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

// Real database implementation for testing
export class TestSetup {
  private static instance: TestSetup;
  private prisma: PrismaClient;

  private constructor() {
    // Use the real database connection
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL || 'file:./test.db',
        },
      },
      log: ['error'], // Only log errors to reduce noise in tests
    });
  }

  static getInstance(): TestSetup {
    if (!TestSetup.instance) {
      TestSetup.instance = new TestSetup();
    }
    return TestSetup.instance;
  }

  async setupTestDatabase() {
    console.log('Setting up real test database...');
    try {
      await this.prisma.$connect();
      console.log('✅ Test database connected successfully');

      // Seed basic test data
      await this.seedTestData();

      return Promise.resolve();
    } catch (error) {
      console.error('❌ Test database setup failed:', error);
      throw error;
    }
  }

  async cleanupTestData() {
    console.log('Cleaning up real test data...');
    try {
      // Clean up test data only (not all data)
      // Be very careful to only delete test data
      await this.prisma.forumReply.deleteMany({
        where: {
          author: {
            email: {
              contains: 'test',
            },
          },
        },
      });

      await this.prisma.forumPost.deleteMany({
        where: {
          author: {
            email: {
              contains: 'test',
            },
          },
        },
      });

      await this.prisma.assessment.deleteMany({
        where: {
          user: {
            email: {
              contains: 'test',
            },
          },
        },
      });

      // Only delete test learning resources (those created by test)
      await this.prisma.learningResource.deleteMany({
        where: {
          title: {
            contains: 'Test',
          },
        },
      });

      // Only delete test users
      await this.prisma.user.deleteMany({
        where: {
          email: {
            contains: 'test',
          },
        },
      });

      console.log('✅ Test data cleaned up successfully');
      return Promise.resolve();
    } catch (error) {
      console.error('❌ Test data cleanup failed:', error);
      throw error;
    }
  }

  async createTestData() {
    // Mock implementation - returns mock test data
    const testUser = {
      id: 'test-user-1',
      email: '<EMAIL>',
      name: 'Test User',
      password: '$2b$10$test.hash.for.testing.purposes.only',
      emailVerified: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return { testUser };
  }

  async disconnect() {
    // Mock implementation
    return Promise.resolve();
  }

  getPrisma() {
    // Return the real Prisma client for testing
    return this.prisma;
  }

  async seedTestData() {
    console.log('Seeding test data...');

    // Create test users
    const hashedPassword = await bcrypt.hash('testpassword123', 10);

    const testUser1 = await this.prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User 1',
        emailVerified: new Date(),
      },
    });

    console.log('Created testUser1:', testUser1);

    if (!testUser1 || !testUser1.id) {
      throw new Error(`Failed to create testUser1 - user object is missing or has no id. Got: ${JSON.stringify(testUser1)}`);
    }

    const testUser2 = await this.prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User 2',
        emailVerified: new Date(),
      },
    });

    // Create test learning resources
    await this.prisma.learningResource.upsert({
      where: { id: 'test-resource-1' },
      update: {},
      create: {
        id: 'test-resource-1',
        title: 'Test Learning Resource 1',
        description: 'A test learning resource for testing purposes',
        url: 'https://example.com/resource1',
        type: 'ARTICLE',
        category: 'WEB_DEVELOPMENT',
        skillLevel: 'BEGINNER',
        format: 'SELF_PACED',
        duration: '30 minutes',
      },
    });

    // Create test assessments
    await this.prisma.assessment.upsert({
      where: { id: 'test-assessment-1' },
      update: {},
      create: {
        id: 'test-assessment-1',
        userId: testUser1.id,
        status: 'COMPLETED',
        currentStep: 5,
        completedAt: new Date(),
        responses: {
          create: [
            {
              questionKey: 'dissatisfaction_triggers',
              answerValue: ['lack_of_growth'],
            },
            {
              questionKey: 'desired_outcomes_skill_a',
              answerValue: 'high',
            },
          ],
        },
      },
    });

    console.log('✅ Test data seeded successfully');
  }

  async disconnect() {
    await this.prisma.$disconnect();
  }
}

// Export mock test data for use in tests
export const mockTestData = {
  user: {
    id: 'test-user-1',
    email: '<EMAIL>',
    name: 'Test User',
    password: '$2b$10$test.hash.for.testing.purposes.only',
    emailVerified: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  learningResource: {
    id: 'test-resource-1',
    title: 'Test Resource 1',
    description: 'A test learning resource',
    url: 'https://example.com/resource1',
    type: 'COURSE',
    difficulty: 'BEGINNER',
    estimatedHours: 10,
    category: 'Technology',
    tags: ['test', 'programming'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  careerPath: {
    id: 'test-career-1',
    title: 'Test Career Path',
    description: 'A test career path',
    category: 'Technology',
    difficulty: 'INTERMEDIATE',
    estimatedDuration: '6 months',
    skills: ['JavaScript', 'React', 'Node.js'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
};

// Mock Next.js server environment
export function setupNextJSMocks() {
  // Mock NextRequest and NextResponse
  global.Request = global.Request || class MockRequest {
    constructor(public url: string, public init?: RequestInit) {}
    json() { return Promise.resolve(this.init?.body ? JSON.parse(this.init.body as string) : {}); }
  };

  global.Response = global.Response || class MockResponse {
    constructor(public body?: any, public init?: ResponseInit) {}
    json() { return Promise.resolve(this.body); }
    static json(data: any, init?: ResponseInit) {
      return new MockResponse(data, init);
    }
  };

  // Mock environment variables
  process.env.NODE_ENV = 'test';
  process.env.NEXTAUTH_SECRET = 'test-secret';
  process.env.NEXTAUTH_URL = 'http://localhost:3000';
  process.env.DATABASE_URL = 'file:./test.db';
}

// Mock authentication
export function createMockSession(userId: string, email: string) {
  return {
    user: {
      id: userId,
      email: email,
      name: 'Test User',
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  };
}

// Test data generators
export const testData = {
  validUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    name: 'Test User'
  },
  
  validAssessment: {
    currentStep: 1,
    formData: {
      dissatisfaction_triggers: ['lack_of_growth'],
      desired_outcomes_skill_a: 'high',
      work_environment_preference: 'remote'
    },
    status: 'IN_PROGRESS'
  },

  validLearningResource: {
    title: 'Test Learning Resource',
    description: 'A comprehensive test resource',
    url: 'https://example.com/resource',
    type: 'COURSE',
    difficulty: 'BEGINNER',
    estimatedHours: 10,
    category: 'Technology',
    tags: ['test', 'learning']
  },

  validForumPost: {
    title: 'Test Forum Post',
    content: 'This is a test forum post content',
    category: 'GENERAL'
  }
};

// Security test inputs
export const securityTestInputs = {
  xssAttempts: [
    '<script>alert("xss")</script>',
    'javascript:alert("xss")',
    '<img src=x onerror=alert("xss")>',
    '<svg onload=alert("xss")>',
  ],
  
  sqlInjectionAttempts: [
    "'; DROP TABLE users; --",
    "1' OR '1'='1",
    "admin'--",
    "' UNION SELECT * FROM users --",
  ],
  
  pathTraversalAttempts: [
    '../../../etc/passwd',
    '..\\..\\..\\windows\\system32\\config\\sam',
    '....//....//....//etc/passwd',
  ],
  
  oversizedInput: 'A'.repeat(10000),
  
  specialCharacters: '!@#$%^&*()_+-=[]{}|;\':",./<>?`~'
};

// Performance testing utilities
export async function measurePerformance<T>(
  operation: () => Promise<T>,
  maxExecutionTime: number = 5000
): Promise<{ result: T; executionTime: number; withinLimit: boolean }> {
  const startTime = performance.now();
  const result = await operation();
  const endTime = performance.now();
  const executionTime = endTime - startTime;

  return {
    result,
    executionTime,
    withinLimit: executionTime <= maxExecutionTime
  };
}

// Test environment setup for browser APIs
export function setupTestEnvironment() {
  // Keep console methods for debugging real database operations
  // Only mock browser APIs that don't exist in Node.js

  // Mock fetch for external API calls
  global.fetch = jest.fn();

  // Mock window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });

  // Mock ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  // Mock IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  // Test environment variables - Use secure test secrets
  process.env.NODE_ENV = 'test';
  process.env.NEXTAUTH_SECRET = 'test-secret-32-chars-minimum-length-required-for-security';
  process.env.NEXTAUTH_URL = 'http://localhost:3000';
  process.env.DATABASE_URL = 'file:./test.db';
}
